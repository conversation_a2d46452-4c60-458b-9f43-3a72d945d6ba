#!/usr/bin/env python3
"""
Simple script to check what's actually on the login page
"""

import requests
from bs4 import BeautifulSoup

BASE_URL = "http://127.0.0.1:5000"

def check_login_page():
    """Check what's actually on the login page"""
    session = requests.Session()
    
    print("=" * 60)
    print("Login Page Content Check")
    print("=" * 60)
    
    # Get login page
    response = session.get(f"{BASE_URL}/login")
    if response.status_code == 200:
        print(f"✅ Login page loaded successfully")
        
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # Check for navbar
        navbar = soup.find('nav')

        # Debug: Show first 1000 characters of HTML
        print(f"\n--- First 1000 characters of HTML ---")
        print(response.text[:1000])
        print(f"--- End of HTML snippet ---\n")

        if navbar:
            print(f"✅ Navbar found")
            
            # Check navbar classes
            navbar_classes = navbar.get('class', [])
            print(f"   Navbar classes: {navbar_classes}")
            
            # Check for brand
            brand = navbar.find('a', class_='navbar-brand')
            if brand:
                print(f"✅ Brand found: {brand.get_text(strip=True)}")
            
            # Check for navigation items
            nav_links = navbar.find_all('a', class_='nav-link')
            print(f"   Found {len(nav_links)} navigation links:")
            for link in nav_links:
                href = link.get('href', '')
                text = link.get_text(strip=True)
                print(f"     - {text} -> {href}")
            
            # Check for login button specifically
            login_buttons = navbar.find_all('a', href=lambda x: x and '/login' in x)
            print(f"   Found {len(login_buttons)} login buttons:")
            for btn in login_buttons:
                text = btn.get_text(strip=True)
                classes = btn.get('class', [])
                print(f"     - {text} (classes: {classes})")
            
            # Check for user menu (should not be present)
            user_menu = navbar.find('span', string=lambda text: text and 'admin' in text.lower())
            if user_menu:
                print(f"❌ User menu found (should not be present when not logged in)")
            else:
                print(f"✅ No user menu found (correct for login page)")
                
        else:
            print(f"❌ No navbar found")
            
        # Check page title
        title = soup.find('title')
        if title:
            print(f"   Page title: {title.get_text(strip=True)}")
            
    else:
        print(f"❌ Failed to load login page: {response.status_code}")
    
    print("\n" + "=" * 60)
    print("Now checking index page...")
    print("=" * 60)
    
    # Check index page too
    response = session.get(f"{BASE_URL}/")
    if response.status_code == 200:
        print(f"✅ Index page loaded successfully")
        
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # Check for navbar
        navbar = soup.find('nav')
        if navbar:
            print(f"✅ Navbar found on index page")
            
            # Check for login button
            login_buttons = navbar.find_all('a', href=lambda x: x and '/login' in x)
            print(f"   Found {len(login_buttons)} login buttons on index page")
            
            # Check for main navigation (should be hidden)
            main_nav_links = navbar.find_all('a', href=lambda x: x and any(path in x for path in ['/dashboard', '/add_file', '/scan']))
            if main_nav_links:
                print(f"❌ Found {len(main_nav_links)} main navigation links (should be hidden)")
                for link in main_nav_links:
                    print(f"     - {link.get_text(strip=True)} -> {link.get('href')}")
            else:
                print(f"✅ No main navigation links found (correct)")
                
        else:
            print(f"❌ No navbar found on index page")
    else:
        print(f"❌ Failed to load index page: {response.status_code}")
    
    print("\n" + "=" * 60)
    print("Summary:")
    print("✅ Navbar should be visible on both login and index pages")
    print("✅ Only login button should be visible (no main navigation)")
    print("✅ No user menu should be present when not authenticated")
    print("=" * 60)

if __name__ == "__main__":
    check_login_page()
